// 调试支付状态处理逻辑
console.log('=== 支付状态调试 ===');

// 模拟您提供的URL参数
const testUrl = 'http://localhost:3000/zh/games/1?paymentStatus=FAILED&paymentMessage=&orderNo=';
const url = new URL(testUrl);
const searchParams = url.searchParams;

console.log('原始URL:', testUrl);
console.log('URL参数:', {
  paymentStatus: searchParams.get('paymentStatus'),
  paymentMessage: searchParams.get('paymentMessage'),
  orderNo: searchParams.get('orderNo')
});

// 模拟游戏详情页面的状态处理逻辑
const rawPaymentStatus = searchParams.get('paymentStatus');
const paymentMessage = searchParams.get('paymentMessage') || '';
const orderNo = searchParams.get('orderNo') || '';

console.log('提取的参数:', { rawPaymentStatus, paymentMessage, orderNo });

if (rawPaymentStatus) {
  // 处理大小写转换和状态映射
  let normalizedStatus;
  const upperStatus = rawPaymentStatus.toUpperCase();
  
  if (upperStatus === 'SUCCESS' || upperStatus === 'COMPLETED') {
    normalizedStatus = 'success';
  } else if (upperStatus === 'FAILED' || upperStatus === 'FAILURE' || upperStatus === 'ERROR') {
    normalizedStatus = 'failed';
  } else if (upperStatus === 'CANCELLED' || upperStatus === 'CANCELED') {
    normalizedStatus = 'cancelled';
  } else {
    // 默认处理未知状态
    normalizedStatus = 'failed';
  }
  
  // 处理空消息的情况
  let displayMessage = paymentMessage;
  if (!displayMessage || displayMessage.trim() === '') {
    switch (normalizedStatus) {
      case 'success':
        displayMessage = '支付已完成，感谢您的购买！';
        break;
      case 'failed':
        displayMessage = '支付过程中出现错误，请重试或联系客服';
        break;
      case 'cancelled':
        displayMessage = '您已取消支付操作';
        break;
    }
  }
  
  const paymentStatusModal = {
    isOpen: true,
    status: normalizedStatus,
    message: decodeURIComponent(displayMessage),
    orderNo: orderNo
  };
  
  console.log('最终弹窗数据:', paymentStatusModal);
  console.log('弹窗应该显示:', {
    isOpen: paymentStatusModal.isOpen,
    status: paymentStatusModal.status,
    title: normalizedStatus === 'failed' ? '支付失败' : normalizedStatus,
    message: paymentStatusModal.message,
    orderNo: paymentStatusModal.orderNo || '无'
  });
} else {
  console.log('没有支付状态参数');
}

console.log('=== 调试结束 ===');
