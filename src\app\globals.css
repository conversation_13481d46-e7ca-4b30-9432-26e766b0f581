@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: var(--font-roboto-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

/* 自定义动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* 导航栏动画类 */
.animate-fadeIn {
  animation: fadeInDown 0.2s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* 导航链接悬停效果 */
.nav-link-hover:hover::after {
  width: 100%;
}

.nav-link-hover::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, #6366f1, #a855f7);
  transition: width 0.3s ease;
}

/* 自定义滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(99, 102, 241, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(99, 102, 241, 0.5);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(99, 102, 241, 0.8);
}

/* 隐藏滚动条但保持滚动功能 */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* 支付弹窗滚动区域优化 */
.payment-scroll-area {
  -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
  overscroll-behavior: contain; /* 防止滚动传播 */
}

/* 移动设备触摸优化 */
@media (max-width: 768px) {
  .payment-scroll-area {
    /* 移动端滚动优化 */
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
  }
  html, body {
    overscroll-behavior: none; /* 防止iOS橡皮筋效果 */
    -webkit-overflow-scrolling: touch; /* 保持滚动惯性 */
    overflow-x: hidden; /* 防止水平滚动 */
  }

  * {
    -webkit-tap-highlight-color: transparent; /* 移除点击高亮 */
  }

  .overflow-scroll, .overflow-auto, .overflow-y-auto, .overflow-y-scroll {
    -webkit-overflow-scrolling: touch; /* 平滑滚动 */
  }

  /* 提高按钮和可点击元素的触摸面积 */
  button, [role="button"], a {
    min-height: 44px;
    min-width: 44px;
  }

  /* 修复固定定位底栏在iOS中的显示问题 */
  .sticky.bottom-0 {
    position: -webkit-sticky;
    position: sticky;
    transform: translateZ(0); /* 开启硬件加速 */
    z-index: 10;
  }

  /* 确保模态框显示正常 */
  [role="dialog"] {
    touch-action: none; /* 禁止模态框触摸事件 */
  }

  /* 优化滚动性能 */
  main {
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
  }

  /* 确保ProductCard不会导致滚动中断 */
  [class*="ProductCard"] {
    transform: translateZ(0);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* 手机端登录按钮优化 */
  button[class*="gradient"] {
    /* 增强触摸反馈 */
    -webkit-tap-highlight-color: rgba(34, 197, 94, 0.3);
    tap-highlight-color: rgba(34, 197, 94, 0.3);

    /* 防止双击缩放 */
    touch-action: manipulation;

    /* 优化按钮按压效果 */
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* 登录按钮特殊效果 */
  button[class*="from-green-600"] {
    /* 添加微妙的阴影动画 */
    box-shadow:
      0 4px 14px 0 rgba(34, 197, 94, 0.25),
      0 2px 4px 0 rgba(0, 0, 0, 0.1);
  }

  /* 按压时的阴影效果 */
  button[class*="from-green-600"]:active {
    box-shadow:
      0 2px 8px 0 rgba(34, 197, 94, 0.35),
      0 1px 2px 0 rgba(0, 0, 0, 0.15);
  }

  /* 手机端按钮文字优化 */
  button span {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  /* 手机端登录区域特殊效果 */
  .mobile-login-area {
    /* 背景动画效果 */
    background: linear-gradient(135deg,
      rgba(34, 197, 94, 0.1) 0%,
      rgba(20, 184, 166, 0.1) 50%,
      rgba(34, 197, 94, 0.05) 100%);

    /* 边框发光效果 */
    border: 1px solid rgba(34, 197, 94, 0.3);
    box-shadow:
      0 0 20px rgba(34, 197, 94, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    /* 动画效果 */
    animation: mobile-login-glow 3s ease-in-out infinite alternate;
  }

  @keyframes mobile-login-glow {
    0% {
      box-shadow:
        0 0 20px rgba(34, 197, 94, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
    100% {
      box-shadow:
        0 0 30px rgba(34, 197, 94, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    }
  }

  /* 手机端登录按钮特殊动画 */
  .mobile-login-button {
    position: relative;
    overflow: hidden;
  }

  .mobile-login-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent);
    transition: left 0.5s;
  }

  .mobile-login-button:hover::before {
    left: 100%;
  }

  /* 手机端用户信息区域特殊效果 */
  .mobile-user-area {
    /* 背景动画效果 */
    background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.1) 0%,
      rgba(147, 51, 234, 0.1) 50%,
      rgba(59, 130, 246, 0.05) 100%);

    /* 边框发光效果 */
    border: 1px solid rgba(59, 130, 246, 0.3);
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    /* 动画效果 */
    animation: mobile-user-glow 4s ease-in-out infinite alternate;
  }

  @keyframes mobile-user-glow {
    0% {
      box-shadow:
        0 0 20px rgba(59, 130, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
    100% {
      box-shadow:
        0 0 30px rgba(59, 130, 246, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    }
  }

  /* 手机端退出登录按钮特殊动画 */
  .mobile-logout-button {
    position: relative;
    overflow: hidden;
  }

  .mobile-logout-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent);
    transition: left 0.5s;
  }

  .mobile-logout-button:hover::before {
    left: 100%;
  }

  /* 退出登录按钮特殊效果 */
  button[class*="from-red-600"] {
    /* 添加微妙的阴影动画 */
    box-shadow:
      0 4px 14px 0 rgba(220, 38, 38, 0.25),
      0 2px 4px 0 rgba(0, 0, 0, 0.1);
  }

  /* 按压时的阴影效果 */
  button[class*="from-red-600"]:active {
    box-shadow:
      0 2px 8px 0 rgba(220, 38, 38, 0.35),
      0 1px 2px 0 rgba(0, 0, 0, 0.15);
  }

  /* 永久悬浮支付栏 - 确保在所有情况下都固定在底部 */
  .payment-bar-floating {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    z-index: 9999 !important;
    background: rgba(24, 24, 27, 0.95);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border-top: 1px solid rgba(113, 113, 122, 0.3);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);

    /* 硬件加速和性能优化 */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;

    /* 确保在iOS Safari中正常工作 */
    -webkit-position: fixed;

    /* 防止被其他元素覆盖 */
    isolation: isolate;
  }

  /* 确保支付栏在所有视口下都能正确显示 */
  @supports (position: fixed) {
    .payment-bar-floating {
      position: fixed !important;
    }
  }

  /* 针对iOS设备的特殊处理 */
  @supports (-webkit-touch-callout: none) {
    .payment-bar-floating {
      position: fixed !important;
      bottom: 0 !important;
      bottom: env(safe-area-inset-bottom, 0) !important;
      padding-bottom: calc(1rem + env(safe-area-inset-bottom, 0)) !important;
    }
  }

  /* 确保支付按钮在手机端有足够的触摸区域 */
  .payment-button-mobile {
    min-height: 48px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
    -webkit-user-select: none;
  }

  /* 支付按钮按压效果优化 */
  .payment-button-mobile:active {
    transform: scale(0.98);
    transition: transform 0.1s ease-in-out;
  }

  /* 防止页面内容被悬浮支付栏遮挡 */
  .content-with-floating-payment {
    padding-bottom: 120px !important; /* 为悬浮支付栏预留空间 */
    margin-bottom: 0 !important;
  }

  @media (min-width: 640px) {
    .content-with-floating-payment {
      padding-bottom: 100px !important; /* 桌面端较小的预留空间 */
    }
  }

  /* 确保页面主体不会影响悬浮支付栏 */
  body {
    position: relative;
    overflow-x: hidden; /* 防止水平滚动影响固定定位 */
  }

  /* 确保主要内容区域不会覆盖支付栏 */
  main {
    position: relative;
    z-index: 1;
    min-height: 100vh;
    padding-bottom: 120px; /* 默认为支付栏预留空间 */
  }

  @media (min-width: 640px) {
    main {
      padding-bottom: 100px;
    }
  }

  /* 防止模态框等高层级元素干扰支付栏 */
  .modal-overlay {
    z-index: 9998 !important; /* 确保低于支付栏 */
  }

  /* 确保支付栏始终可见 */
  .payment-floating-container {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 9999 !important;
    pointer-events: auto !important;
  }

  /* 全局样式确保悬浮支付栏正常工作 */
  html {
    position: relative;
    overflow-x: hidden;
  }

  /* 确保根容器不会干扰固定定位 */
  #__next {
    position: relative;
    min-height: 100vh;
  }

  /* 移动端视口优化 */
  @media screen and (max-width: 768px) {
    html {
      /* 防止iOS Safari地址栏影响视口高度 */
      height: -webkit-fill-available;
    }

    body {
      /* 使用动态视口高度 */
      min-height: 100vh;
      min-height: -webkit-fill-available;
    }
  }

  /* 确保悬浮支付栏在所有情况下都能正确显示 */
  [data-floating-payment-bar] {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100vw !important;
    z-index: 9999 !important;

    /* 防止被transform影响 */
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;

    /* 确保在所有浏览器中都能正确渲染 */
    will-change: transform;
    contain: layout style paint;
  }

  /* iOS Safari 特殊处理 */
  @supports (-webkit-touch-callout: none) {
    [data-floating-payment-bar] {
      /* 适配安全区域 */
      padding-bottom: calc(1rem + env(safe-area-inset-bottom, 0)) !important;
      bottom: env(safe-area-inset-bottom, 0) !important;
    }
  }

  /* 支付栏动画效果 */
  .payment-bar-slide-up {
    animation: slideUpFromBottom 0.3s ease-out forwards;
  }

  @keyframes slideUpFromBottom {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* 支付按钮特殊效果 */
  .payment-button-gradient {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    box-shadow:
      0 4px 14px 0 rgba(79, 70, 229, 0.25),
      0 2px 4px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .payment-button-gradient:hover {
    background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
    box-shadow:
      0 6px 20px 0 rgba(79, 70, 229, 0.35),
      0 4px 8px 0 rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }

  .payment-button-gradient:active {
    transform: translateY(0) scale(0.98);
    box-shadow:
      0 2px 8px 0 rgba(79, 70, 229, 0.35),
      0 1px 2px 0 rgba(0, 0, 0, 0.15);
  }

  /* 悬浮支付栏样式优化 */
  .floating-payment-bar {
    /* 客服按钮已移到上方，不需要预留水平空间 */
  }

  /* 客服按钮位置优化 - 确保在支付栏上方合适位置 */
  .customer-service-floating {
    bottom: calc(100px + env(safe-area-inset-bottom, 0)) !important;
    transition: bottom 0.3s ease;
  }

  /* 响应式调整客服按钮位置 */
  @media (min-width: 640px) {
    .customer-service-floating {
      bottom: calc(90px + env(safe-area-inset-bottom, 0)) !important;
    }
  }

  @media (min-width: 768px) {
    .customer-service-floating {
      bottom: calc(80px + env(safe-area-inset-bottom, 0)) !important;
    }
  }


}
