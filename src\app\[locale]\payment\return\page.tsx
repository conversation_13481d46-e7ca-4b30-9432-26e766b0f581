"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslations, useLocale } from "next-intl";
import Layout from "../../../components/Layout";

export default function PaymentReturnPage() {
  const [status, setStatus] = useState<"loading" | "success" | "failed" | "cancelled">("loading");
  const [message, setMessage] = useState<string>("");
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations();
  const locale = useLocale();

  useEffect(() => {
    // 从URL参数中获取支付结果 - 支持多种参数名
    const rawPaymentStatus = searchParams.get("status") || searchParams.get("paymentStatus");
    const paymentMessage = searchParams.get("message") || searchParams.get("paymentMessage");
    const orderNo = searchParams.get("orderNo");
    const gameId = searchParams.get("gameId"); // 获取游戏ID

    console.log("支付返回参数:", { rawPaymentStatus, paymentMessage, orderNo, gameId });

    // 标准化支付状态值
    let normalizedStatus: "success" | "failed" | "cancelled" = "success";
    let normalizedMessage = paymentMessage || '';

    if (rawPaymentStatus) {
      const upperStatus = rawPaymentStatus.toUpperCase();

      // 如果是 PENDING 状态，直接跳转到游戏页面，不显示状态信息
      if (upperStatus === 'PENDING' || upperStatus === 'PROCESSING') {
        console.log('支付状态为 PENDING，直接跳转到游戏页面');
        if (gameId) {
          router.push(`/${locale}/games/${gameId}`);
        } else {
          router.push(`/${locale}`);
        }
        return;
      }

      if (upperStatus === 'SUCCESS' || upperStatus === 'COMPLETED' || upperStatus === 'PAID') {
        normalizedStatus = "success";
        normalizedMessage = normalizedMessage || t('payment.paymentSuccessMessage');
      } else if (upperStatus === 'FAILED' || upperStatus === 'FAILURE' || upperStatus === 'ERROR') {
        normalizedStatus = "failed";
        normalizedMessage = normalizedMessage || t('payment.paymentFailedMessage');
      } else if (upperStatus === 'CANCELLED' || upperStatus === 'CANCELED' || upperStatus === 'CANCEL') {
        normalizedStatus = "cancelled";
        normalizedMessage = normalizedMessage || t('payment.paymentCancelledMessage');
      } else {
        // 未知状态，默认为失败
        normalizedStatus = "failed";
        normalizedMessage = normalizedMessage || `${t('payment.paymentFailed')}: ${rawPaymentStatus}`;
      }
    } else {
      // 如果没有状态参数，默认为成功
      normalizedStatus = "success";
      normalizedMessage = t('payment.paymentCompleted');
    }

    // 设置页面状态
    setStatus(normalizedStatus);
    setMessage(normalizedMessage);

    // 构建返回URL - 如果有游戏ID则跳转到游戏详情页，否则跳转到首页
    // 在URL中包含支付状态信息，使用标准化后的状态值
    const returnUrl = gameId
      ? `${window.location.origin}/${locale}/games/${gameId}?paymentStatus=${normalizedStatus}&paymentMessage=${encodeURIComponent(normalizedMessage)}&orderNo=${orderNo || ''}`
      : `${window.location.origin}/${locale}`;

    console.log("构建的返回URL:", returnUrl);

    // 检查是否在弹窗中（有父窗口）
    if (window.opener && window.opener !== window) {
      // 在弹窗中，向父窗口发送消息
      window.opener.postMessage({
        type: 'PAYMENT_COMPLETE',
        returnUrl: returnUrl,
        status: normalizedStatus,
        message: normalizedMessage,
        orderNo: orderNo,
        gameId: gameId
      }, window.location.origin);

      // 关闭当前弹窗
      window.close();
    } else {
      // 不在弹窗中，正常跳转
      const timer = setTimeout(() => {
        if (gameId) {
          router.push(`/${locale}/games/${gameId}?paymentStatus=${normalizedStatus}&paymentMessage=${encodeURIComponent(normalizedMessage)}&orderNo=${orderNo || ''}`);
        } else {
          router.push(`/${locale}`);
        }
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [searchParams, router, locale, t]);

  const handleBackToGame = () => {
    const gameId = searchParams.get("gameId");
    const orderNo = searchParams.get("orderNo");
    if (gameId) {
      router.push(`/${locale}/games/${gameId}?paymentStatus=${status}&paymentMessage=${encodeURIComponent(message || '')}&orderNo=${orderNo || ''}`);
    } else {
      router.push(`/${locale}`);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case "success":
        return (
          <div className="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center mb-6">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-12 w-12 text-green-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        );
      case "failed":
        return (
          <div className="w-20 h-20 bg-red-500/20 rounded-full flex items-center justify-center mb-6">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-12 w-12 text-red-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </div>
        );
      case "cancelled":
        return (
          <div className="w-20 h-20 bg-yellow-500/20 rounded-full flex items-center justify-center mb-6">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-12 w-12 text-yellow-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-20 h-20 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin mb-6"></div>
        );
    }
  };

  const getStatusTitle = () => {
    switch (status) {
      case "success":
        return t('payment.paymentSuccess');
      case "failed":
        return t('payment.paymentFailed');
      case "cancelled":
        return t('payment.paymentCancelled');
      default:
        return t('payment.processing');
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case "success":
        return "text-green-400";
      case "failed":
        return "text-red-400";
      case "cancelled":
        return "text-yellow-400";
      default:
        return "text-white";
    }
  };

  return (
    <Layout>
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="max-w-md w-full mx-auto text-center">
          <div className="bg-zinc-900/50 rounded-2xl p-8 border border-zinc-800">
            {getStatusIcon()}
            
            <h1 className={`text-2xl font-bold mb-4 ${getStatusColor()}`}>
              {getStatusTitle()}
            </h1>
            
            {message && (
              <p className="text-zinc-400 mb-6 leading-relaxed">
                {message}
              </p>
            )}
            
            <div className="space-y-4">
              <button
                onClick={handleBackToGame}
                className="w-full py-3 px-6 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium rounded-lg transition-colors"
              >
                {searchParams.get("gameId") ? t('ui.backToGame') : t('ui.backToHome')}
              </button>
              
              {status === "loading" && (
                <p className="text-sm text-zinc-500">
                  {t('payment.autoRedirect')}
                </p>
              )}
              
              {status !== "loading" && (
                <p className="text-sm text-zinc-500">
                  {searchParams.get("gameId") ? "3秒后自动跳转到游戏详情页" : "3秒后自动跳转到首页"}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
