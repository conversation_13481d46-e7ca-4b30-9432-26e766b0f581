"use client";

import { useCallback, useRef } from "react";
import Image from "next/image";
import { getCurrencySymbol } from "../lib/games/utils";

interface ProductCardProps {
  id: string;
  title: string;
  imageUrl: string;
  price: number;
  originalPrice?: number | null;
  currency?: string;
  description: string;
  onSelect: (id: string) => void;
  selected: boolean;
}

export default function ProductCard({
  id,
  title,
  imageUrl,
  price,
  originalPrice,
  currency = undefined,
  description,
  onSelect,
  selected,
}: ProductCardProps) {
  // 计算折扣，并检查原价是否大于当前价格
  const hasDiscount = originalPrice && originalPrice > price;
  const discount = hasDiscount ? Math.round(((originalPrice - price) / originalPrice) * 100) : 0;
  const currencySymbol = getCurrencySymbol(currency);
  
  // 增强的触摸事件处理
  const touchStartRef = useRef<{x: number, y: number, time: number} | null>(null);
  
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    // 记录触摸开始的位置、时间
    touchStartRef.current = {
      x: e.touches[0].clientX,
      y: e.touches[0].clientY,
      time: Date.now()
    };
    
    // 阻止默认行为防止可能的滚动冲突
    e.stopPropagation();
  }, []);
  
  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!touchStartRef.current) return;
    
    const touchMove = {
      x: e.touches[0].clientX,
      y: e.touches[0].clientY
    };
    
    // 计算垂直和水平移动距离
    const diffX = Math.abs(touchMove.x - touchStartRef.current.x);
    const diffY = Math.abs(touchMove.y - touchStartRef.current.y);
    
    // 如果用户明显是想滚动（垂直移动 > 水平移动），则取消点击意图
    if (diffY > 10 && diffY > diffX) {
      touchStartRef.current = null;
    }
  }, []);
  
  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    // 如果没有开始触摸记录，或者已被取消，则不处理
    if (!touchStartRef.current) return;
    
    const touchEnd = {
      x: e.changedTouches[0].clientX,
      y: e.changedTouches[0].clientY,
      time: Date.now()
    };
    
    // 计算移动距离和时间
    const diffX = Math.abs(touchEnd.x - touchStartRef.current.x);
    const diffY = Math.abs(touchEnd.y - touchStartRef.current.y);
    const diffTime = touchEnd.time - touchStartRef.current.time;
    
    // 判断是点击而不是滚动：
    // 1. 移动距离很小 (<10px)
    // 2. 接触时间短 (<300ms)
    // 3. 水平移动不大于垂直移动
    if ((diffX < 10 && diffY < 10) || (diffTime < 300 && diffX <= diffY)) {
      e.preventDefault(); // 阻止默认行为
      e.stopPropagation(); // 阻止冒泡
      onSelect(id);
    }
    
    // 重置触摸状态
    touchStartRef.current = null;
  }, [id, onSelect]);
  
  // 桌面端点击处理
  const handleClick = useCallback((e: React.MouseEvent) => {
    // 阻止事件冒泡
    e.stopPropagation();
    onSelect(id);
  }, [id, onSelect]);

  return (
    <div
      onClick={handleClick}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      className={`relative overflow-hidden rounded-xl p-4 transition-all duration-300 opacity-100 translate-y-0 ${
        selected
          ? "bg-gradient-to-br from-blue-500/20 to-blue-600/20 border-2 border-blue-400"
          : "bg-white/5 border border-white/10 hover:border-blue-400/50"
      }`}
      style={{
        animation: "fadeIn 0.5s ease-out forwards",
        touchAction: 'pan-y', // 只允许垂直滚动
      }}
    >
      {/* 手机端垂直布局，桌面端水平布局 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-3 sm:space-y-0">
        <div className="relative w-full h-24 sm:w-36 sm:h-36 rounded-xl overflow-hidden bg-gradient-to-br from-white/10 to-white/5 flex items-center justify-center shadow-md">
          <Image
            src={imageUrl}
            alt={title}
            width={108}
            height={108}
            className={`object-contain transition-all duration-300 ${selected ? 'scale-95' : 'hover:scale-105'}`}
            priority
          />
          {selected && (
            <div
              className="absolute inset-0 bg-gradient-to-br from-blue-500/30 to-blue-600/30 flex items-center justify-center transform scale-100 transition-all duration-300"
              style={{
                animation: "scaleIn 0.3s ease-out forwards",
              }}
            >
              <div className="bg-white/90 rounded-full p-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 sm:h-8 sm:w-8 text-blue-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  strokeWidth={2.5}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            </div>
          )}
        </div>
        <div className="flex-1 text-center sm:text-left">
          <h3 className="text-base sm:text-lg font-bold text-white mb-1">{title}</h3>
          <p className="text-xs sm:text-sm text-zinc-400 line-clamp-1 hidden sm:block">{description}</p>
        </div>
      </div>
      <div className="mt-3 sm:mt-4 flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-2 sm:space-y-0">
        <div className="flex items-center justify-center sm:justify-start space-x-2">
          <span className="text-lg sm:text-xl font-bold text-white">{currencySymbol}{price}</span>
          {hasDiscount && (
            <>
              <span className="text-xs sm:text-sm text-zinc-500 line-through">{currencySymbol}{originalPrice}</span>
              <span className="text-xs font-semibold px-1.5 py-0.5 rounded-md bg-green-500/20 text-green-400">
                -{discount}%
              </span>
            </>
          )}
        </div>
        <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all mx-auto sm:mx-0 ${
          selected ? "border-indigo-500 bg-indigo-500" : "border-zinc-600"
        }`}>
          {selected && (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth={2}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M5 13l4 4L19 7"
              />
            </svg>
          )}
        </div>
      </div>
    </div>
  );
} 