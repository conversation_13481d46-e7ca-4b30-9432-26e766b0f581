// 支付方案配置
export const PAYMENT_CONFIG = {
  // 选择支付方案：
  // 'postMessage' - 弹窗 + postMessage 通信（推荐）
  // 'directRedirect' - 直接主页面跳转（最简单）
  // 'iframe' - iframe 嵌入（需要第三方支付支持）
  method: 'postMessage' as 'postMessage' | 'directRedirect' | 'iframe',
  
  // 弹窗配置
  popup: {
    width: 800,
    height: 600,
    features: 'toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes'
  },
  
  // postMessage 配置
  postMessage: {
    // 是否验证消息来源
    verifyOrigin: true,
    // 支付窗口关闭检查间隔（毫秒）
    closeCheckInterval: 1000
  }
};

// 支付方案实现
export class PaymentHandler {
  
  /**
   * 方案一：postMessage 通信
   */
  static handlePostMessage(
    paymentUrl: string, 
    onSuccess: () => void, 
    onError: (error: string) => void,
    t: (key: string) => string
  ) {
    const { width, height, features } = PAYMENT_CONFIG.popup;
    const left = (window.screen.width - width) / 2;
    const top = (window.screen.height - height) / 2;

    const paymentWindow = window.open(
      paymentUrl,
      'payment_window',
      `width=${width},height=${height},left=${left},top=${top},${features}`
    );

    if (!paymentWindow) {
      onError(t('payment.allowPopup'));
      return;
    }

    // 监听支付窗口的消息
    const handleMessage = (event: MessageEvent) => {
      if (PAYMENT_CONFIG.postMessage.verifyOrigin && event.origin !== window.location.origin) {
        return;
      }

      if (event.data.type === 'PAYMENT_COMPLETE') {
        // 支付完成，在主窗口跳转
        window.location.href = event.data.returnUrl;
        // 清理事件监听器
        window.removeEventListener('message', handleMessage);
        // 关闭支付窗口
        paymentWindow.close();
      }
    };

    // 添加消息监听器
    window.addEventListener('message', handleMessage);

    // 监听支付窗口关闭事件
    const checkClosed = setInterval(() => {
      if (paymentWindow.closed) {
        clearInterval(checkClosed);
        window.removeEventListener('message', handleMessage);
        // 用户手动关闭了支付窗口
      }
    }, PAYMENT_CONFIG.postMessage.closeCheckInterval);

    onSuccess();
  }

  /**
   * 方案二：直接主页面跳转
   */
  static handleDirectRedirect(paymentUrl: string) {
    window.location.href = paymentUrl;
  }

  /**
   * 方案三：iframe 嵌入（需要第三方支付支持）
   */
  static handleIframe(
    paymentUrl: string, 
    container: HTMLElement,
    onSuccess: () => void,
    onError: (error: string) => void
  ) {
    const iframe = document.createElement('iframe');
    iframe.src = paymentUrl;
    iframe.style.width = '100%';
    iframe.style.height = '100%';
    iframe.style.border = 'none';
    
    // 监听 iframe 加载
    iframe.onload = () => {
      try {
        // 注意：由于跨域限制，可能无法访问 iframe 内容
        // 这种方案需要第三方支付页面的配合
        onSuccess();
      } catch (error) {
        onError('iframe 加载失败');
      }
    };

    container.appendChild(iframe);
  }

  /**
   * 统一支付处理入口
   */
  static handle(
    paymentUrl: string,
    onSuccess: () => void,
    onError: (error: string) => void,
    t: (key: string) => string,
    iframeContainer?: HTMLElement
  ) {
    switch (PAYMENT_CONFIG.method) {
      case 'postMessage':
        this.handlePostMessage(paymentUrl, onSuccess, onError, t);
        break;
      case 'directRedirect':
        this.handleDirectRedirect(paymentUrl);
        break;
      case 'iframe':
        if (iframeContainer) {
          this.handleIframe(paymentUrl, iframeContainer, onSuccess, onError);
        } else {
          onError('iframe 容器未提供');
        }
        break;
      default:
        onError('未知的支付方案');
    }
  }
}
