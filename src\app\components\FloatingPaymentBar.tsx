"use client";

import { ReactNode } from 'react';

interface FloatingPaymentBarProps {
  children: ReactNode;
  highlight?: boolean;
  className?: string;
}

export default function FloatingPaymentBar({
  children,
  highlight = false,
  className = ''
}: FloatingPaymentBarProps) {
  return (
    <div
      style={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        width: '100%',
        height: 'auto',
        zIndex: 9998,
        backgroundColor: highlight
          ? 'rgba(24, 24, 27, 0.98)'
          : 'rgba(24, 24, 27, 0.95)',
        padding: '16px',
        borderTop: highlight
          ? '1px solid rgba(99, 102, 241, 0.7)'
          : '1px solid rgba(113, 113, 122, 0.5)',
        backdropFilter: 'blur(16px)',
        WebkitBackdropFilter: 'blur(16px)',
        boxShadow: highlight
          ? '0 -4px 20px rgba(99, 102, 241, 0.2), 0 -8px 40px rgba(0, 0, 0, 0.3)'
          : '0 -4px 20px rgba(0, 0, 0, 0.3)',
        transition: 'all 0.5s ease',
        // iOS安全区域适配
        paddingBottom: 'calc(16px + env(safe-area-inset-bottom, 0))',
      }}
      className={`floating-payment-bar ${className}`}
    >
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {children}
      </div>
    </div>
  );
}
